package com.ylpz.core.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 排行榜查询请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RankingRequest对象", description = "排行榜查询请求对象")
public class RankingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排行类型：week-周榜，month-月榜，quarter-季度榜，year-年度榜")
    @NotBlank(message = "排行类型不能为空")
    @Pattern(regexp = "^(week|month|quarter|year)$", message = "排行类型只能是week、month、quarter、year")
    private String rankType;

    @ApiModelProperty(value = "年份，默认当前年份")
    private Integer year;

    @ApiModelProperty(value = "奖励状态：待发放、已发放、已取消")
    private String rewardStatus;

    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd")
    private String endTime;
}
