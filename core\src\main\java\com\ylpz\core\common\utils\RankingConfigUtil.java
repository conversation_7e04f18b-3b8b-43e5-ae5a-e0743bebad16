package com.ylpz.core.common.utils;

import java.math.BigDecimal;

/**
 * 排行榜配置工具类
 * 提供排行榜相关的配置常量和工具方法
 */
public class RankingConfigUtil {

    /**
     * 默认排行榜显示数量
     */
    public static final Integer DEFAULT_DISPLAY_COUNT = 20;

    /**
     * 默认奖励人数
     */
    public static final Integer DEFAULT_REWARD_COUNT = 3;

    /**
     * 默认入榜门槛值
     */
    public static final class ThresholdConfig {
        public static final BigDecimal WEEK_THRESHOLD = new BigDecimal("10000");
        public static final BigDecimal MONTH_THRESHOLD = new BigDecimal("30000");
        public static final BigDecimal QUARTER_THRESHOLD = new BigDecimal("100000");
        public static final BigDecimal YEAR_THRESHOLD = new BigDecimal("500000");
    }

    /**
     * 默认奖励金额配置
     */
    public static final class RewardConfig {
        // 周榜奖励
        public static final BigDecimal WEEK_FIRST = new BigDecimal("500");
        public static final BigDecimal WEEK_SECOND = new BigDecimal("300");
        public static final BigDecimal WEEK_THIRD = new BigDecimal("200");

        // 月榜奖励
        public static final BigDecimal MONTH_FIRST = new BigDecimal("1000");
        public static final BigDecimal MONTH_SECOND = new BigDecimal("600");
        public static final BigDecimal MONTH_THIRD = new BigDecimal("400");

        // 季度榜奖励
        public static final BigDecimal QUARTER_FIRST = new BigDecimal("2000");
        public static final BigDecimal QUARTER_SECOND = new BigDecimal("1200");
        public static final BigDecimal QUARTER_THIRD = new BigDecimal("800");

        // 年榜奖励
        public static final BigDecimal YEAR_FIRST = new BigDecimal("5000");
        public static final BigDecimal YEAR_SECOND = new BigDecimal("3000");
        public static final BigDecimal YEAR_THIRD = new BigDecimal("2000");
    }

    /**
     * 获取排行榜入榜门槛值
     *
     * @param rankType 排行榜类型
     * @return 门槛值
     */
    public static BigDecimal getThreshold(String rankType) {
        switch (rankType) {
            case "week":
                return ThresholdConfig.WEEK_THRESHOLD;
            case "month":
                return ThresholdConfig.MONTH_THRESHOLD;
            case "quarter":
                return ThresholdConfig.QUARTER_THRESHOLD;
            case "year":
                return ThresholdConfig.YEAR_THRESHOLD;
            default:
                return ThresholdConfig.WEEK_THRESHOLD;
        }
    }

    /**
     * 获取排行榜奖励金额
     *
     * @param rankType 排行榜类型
     * @param rank     排名
     * @return 奖励金额
     */
    public static BigDecimal getRewardAmount(String rankType, Integer rank) {
        switch (rankType) {
            case "week":
                switch (rank) {
                    case 1: return RewardConfig.WEEK_FIRST;
                    case 2: return RewardConfig.WEEK_SECOND;
                    case 3: return RewardConfig.WEEK_THIRD;
                    default: return BigDecimal.ZERO;
                }
            case "month":
                switch (rank) {
                    case 1: return RewardConfig.MONTH_FIRST;
                    case 2: return RewardConfig.MONTH_SECOND;
                    case 3: return RewardConfig.MONTH_THIRD;
                    default: return BigDecimal.ZERO;
                }
            case "quarter":
                switch (rank) {
                    case 1: return RewardConfig.QUARTER_FIRST;
                    case 2: return RewardConfig.QUARTER_SECOND;
                    case 3: return RewardConfig.QUARTER_THIRD;
                    default: return BigDecimal.ZERO;
                }
            case "year":
                switch (rank) {
                    case 1: return RewardConfig.YEAR_FIRST;
                    case 2: return RewardConfig.YEAR_SECOND;
                    case 3: return RewardConfig.YEAR_THIRD;
                    default: return BigDecimal.ZERO;
                }
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取排行榜类型中文名称
     *
     * @param rankType 排行榜类型
     * @return 中文名称
     */
    public static String getRankTypeName(String rankType) {
        switch (rankType) {
            case "week":
                return "周榜";
            case "month":
                return "月榜";
            case "quarter":
                return "季度榜";
            case "year":
                return "年度榜";
            default:
                return rankType;
        }
    }

    /**
     * 验证排行榜类型是否有效
     *
     * @param rankType 排行榜类型
     * @return 是否有效
     */
    public static boolean isValidRankType(String rankType) {
        return "week".equals(rankType) || "month".equals(rankType) 
            || "quarter".equals(rankType) || "year".equals(rankType);
    }
}
