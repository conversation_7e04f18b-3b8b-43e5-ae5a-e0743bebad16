package com.ylpz.admin.controller;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SalesDataRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.SalesDataDetailResponse;
import com.ylpz.core.common.response.SalesDataResponse;
import com.ylpz.core.common.response.SalesDataStatisticsResponse;
import com.ylpz.core.service.SalesDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 销售数据管理控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/sales/data")
@Api(tags = "销售数据管理")
@Validated
public class SalesDataController {

    @Autowired
    private SalesDataService salesDataService;

    /**
     * 获取销售数据列表
     */
    //@PreAuthorize("hasAuthority('admin:sales:data:list')")
    @ApiOperation(value = "获取销售数据列表")
    @GetMapping("/list")
    public CommonResult<CommonPage<SalesDataResponse>> getSalesDataList(
            @ModelAttribute @Validated SalesDataRequest request,
            @ModelAttribute PageParamRequest pageParamRequest) {

        PageInfo<SalesDataResponse> pageInfo = salesDataService.getSalesDataList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }

    /**
     * 获取销售数据统计
     */
    //@PreAuthorize("hasAuthority('admin:sales:data:statistics')")
    @ApiOperation(value = "获取销售数据统计")
    @GetMapping("/statistics")
    public CommonResult<SalesDataStatisticsResponse> getSalesDataStatistics(
            @ModelAttribute @Validated SalesDataRequest request) {

        SalesDataStatisticsResponse statistics = salesDataService.getSalesDataStatistics(request);
        return CommonResult.success(statistics);
    }

    /**
     * 获取会员销售明细
     */
    //@PreAuthorize("hasAuthority('admin:sales:data:detail')")
    @ApiOperation(value = "获取会员销售明细")
    @GetMapping("/detail/{uid}")
    public CommonResult<CommonPage<SalesDataDetailResponse>> getSalesDataDetail(
            @PathVariable Integer uid,
            @ModelAttribute @Validated SalesDataRequest request,
            @ModelAttribute PageParamRequest pageParamRequest) {

        PageInfo<SalesDataDetailResponse> pageInfo = salesDataService.getSalesDataDetail(uid, request,
                pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }

    /**
     * 导出销售数据
     */
    //@PreAuthorize("hasAuthority('admin:sales:data:export')")
    @ApiOperation(value = "导出销售数据")
    @GetMapping("/export")
    public void exportSalesData(
            @ModelAttribute @Validated SalesDataRequest request,
            HttpServletResponse response) throws IOException {

        try {
            salesDataService.exportSalesData(request, response);
        } catch (Exception e) {
            log.error("导出销售数据失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }
}
