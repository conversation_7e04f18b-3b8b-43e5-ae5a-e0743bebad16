package com.ylpz.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylpz.core.common.request.RankingRequest;
import com.ylpz.core.common.response.RankingResponse;
import com.ylpz.core.common.response.RankingStatisticsResponse;
import com.ylpz.model.ranking.RankingLeaderboard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 排行榜主表 Mapper 接口
 */
@Mapper
public interface RankingLeaderboardMapper extends BaseMapper<RankingLeaderboard> {

    /**
     * 分页查询排行榜列表
     *
     * @param page    分页对象
     * @param request 查询条件
     * @return 排行榜列表
     */
    IPage<RankingResponse> selectRankingList(Page<RankingResponse> page, @Param("request") RankingRequest request);

    /**
     * 获取排行榜统计数据
     *
     * @param request 查询条件
     * @return 统计数据
     */
    RankingStatisticsResponse selectRankingStatistics(@Param("request") RankingRequest request);

    /**
     * 根据排行类型和时间范围查询排行榜
     *
     * @param rankType  排行类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 排行榜信息
     */
    RankingLeaderboard selectByRankTypeAndDate(@Param("rankType") String rankType,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);
}
