package com.ylpz.core.task;

import com.ylpz.core.service.RankingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 排行榜定时任务
 */
@Slf4j
@Component
public class RankingTask {

    @Autowired
    private RankingService rankingService;

    /**
     * 自动生成排行榜
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void autoGenerateRanking() {
        log.info("开始执行排行榜自动生成任务");
        try {
            rankingService.autoGenerateRanking();
            log.info("排行榜自动生成任务执行完成");
        } catch (Exception e) {
            log.error("排行榜自动生成任务执行失败", e);
        }
    }

    /**
     * 手动触发排行榜生成（用于测试）
     */
    public void manualGenerateRanking() {
        log.info("手动触发排行榜生成任务");
        try {
            rankingService.autoGenerateRanking();
            log.info("手动排行榜生成任务执行完成");
        } catch (Exception e) {
            log.error("手动排行榜生成任务执行失败", e);
        }
    }
}
