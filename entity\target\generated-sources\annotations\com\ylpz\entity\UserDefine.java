package com.ylpz.entity;

public interface UserDefine {
    String birthday = "birthday";
    String country = "country";
    String tagId = "tagId";
    String lastIp = "lastIp";
    String loginType = "loginType";
    String groupId = "groupId";
    String experience = "experience";
    String path = "path";
    String vipTime = "vipTime";
    String integral = "integral";
    String promoterTime = "promoterTime";
    String nickname = "nickname";
    String spreadTime = "spreadTime";
    String brokeragePrice = "brokeragePrice";
    String payCount = "payCount";
    String id = "id";
    String nowMoney = "nowMoney";
    String isPromoter = "isPromoter";
    String level = "level";
    String subscribe = "subscribe";
    String openid = "openid";
    String sex = "sex";
    String addres = "addres";
    String updateTime = "updateTime";
    String avatar = "avatar";
    String totalConsumeMoney = "totalConsumeMoney";
    String addIp = "addIp";
    String spreadCount = "spreadCount";
    String realName = "realName";
    String signNum = "signNum";
    String lastLoginTime = "lastLoginTime";
    String cleanTime = "cleanTime";
    String svipTime = "svipTime";
    String phone = "phone";
    String createTime = "createTime";
    String cardId = "cardId";
    String adminid = "adminid";
    String spreadUid = "spreadUid";
    String partnerId = "partnerId";
    String userType = "userType";
    String pwd = "pwd";
    String account = "account";
    String mark = "mark";
    String status = "status";
}
