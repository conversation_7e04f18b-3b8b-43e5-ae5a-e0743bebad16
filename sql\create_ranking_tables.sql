-- 创建排行榜相关表

-- 检查并创建排行榜主表
CREATE TABLE IF NOT EXISTS `ranking_leaderboard` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '排行榜ID',
  `rank_type` varchar(20) NOT NULL COMMENT '排行类型：week-周榜，month-月榜，quarter-季度榜，year-年度榜',
  `rank_period` varchar(100) NOT NULL COMMENT '排行周期描述，如"2025年1月27日-2月5日"',
  `start_date` date NOT NULL COMMENT '统计开始日期',
  `end_date` date NOT NULL COMMENT '统计结束日期',
  `total_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总销售金额',
  `participant_count` int(11) NOT NULL DEFAULT '0' COMMENT '参与人数',
  `reward_status` varchar(20) NOT NULL DEFAULT '待发放' COMMENT '奖励状态：待发放、已发放、已取消',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '奖励总金额',
  `is_auto_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否自动发放奖励：1-是，0-否',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rank_type` (`rank_type`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_reward_status` (`reward_status`),
  UNIQUE KEY `uk_rank_type_date` (`rank_type`, `start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜主表';

-- 检查并创建排行榜明细表
CREATE TABLE IF NOT EXISTS `ranking_leaderboard_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `ranking_id` int(11) NOT NULL COMMENT '排行榜ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `rank` int(11) NOT NULL COMMENT '排名',
  `sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `order_count` int(11) NOT NULL DEFAULT '0' COMMENT '订单数量',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '奖励金额',
  `reward_status` varchar(20) DEFAULT '待发放' COMMENT '奖励状态：待发放、已发放、无奖励',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_ranking_id` (`ranking_id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_rank` (`rank`),
  UNIQUE KEY `uk_ranking_uid` (`ranking_id`, `uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜明细表';

-- 插入测试数据
INSERT IGNORE INTO `ranking_leaderboard` (`rank_type`, `rank_period`, `start_date`, `end_date`, `total_sales_amount`, `participant_count`, `reward_status`, `reward_amount`, `is_auto_reward`) VALUES
('week', '2025年1月27日-2月5日', '2025-01-27', '2025-02-05', 48596.39, 36, '待发放', 0.00, 1),
('week', '2025年1月20日-1月26日', '2025-01-20', '2025-01-26', 48596.39, 53, '已发放', 500.00, 1),
('week', '2025年1月13日-1月19日', '2025-01-13', '2025-01-19', 48596.39, 36, '已发放', 300.00, 1),
('week', '2025年1月6日-1月12日', '2025-01-06', '2025-01-12', 48596.39, 36, '已发放', 200.00, 1),
('week', '2025年1月1日-1月5日', '2025-01-01', '2025-01-05', 48596.39, 13, '已发放', 100.00, 0);

-- 插入排行榜明细测试数据（假设有一些用户数据）
INSERT IGNORE INTO `ranking_leaderboard_detail` (`ranking_id`, `uid`, `rank`, `sales_amount`, `order_count`, `reward_amount`, `reward_status`) VALUES
(1, 1, 1, 8596.39, 15, 0.00, '待发放'),
(1, 2, 2, 7596.39, 12, 0.00, '待发放'),
(1, 3, 3, 6596.39, 10, 0.00, '待发放'),
(1, 4, 4, 5596.39, 8, 0.00, '待发放'),
(1, 5, 5, 4596.39, 6, 0.00, '待发放'),
(2, 1, 1, 9596.39, 18, 479.82, '已发放'),
(2, 2, 2, 8596.39, 15, 257.89, '已发放'),
(2, 3, 3, 7596.39, 12, 227.89, '已发放'),
(2, 4, 4, 6596.39, 10, 65.96, '已发放'),
(2, 5, 5, 5596.39, 8, 55.96, '已发放');

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS `idx_ranking_create_time` ON `ranking_leaderboard` (`create_time`);
CREATE INDEX IF NOT EXISTS `idx_ranking_detail_create_time` ON `ranking_leaderboard_detail` (`create_time`);
CREATE INDEX IF NOT EXISTS `idx_ranking_detail_sales_amount` ON `ranking_leaderboard_detail` (`sales_amount`);

-- 添加外键约束（可选，根据实际需要）
-- ALTER TABLE `ranking_leaderboard_detail` ADD CONSTRAINT `fk_ranking_detail_ranking` FOREIGN KEY (`ranking_id`) REFERENCES `ranking_leaderboard` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `ranking_leaderboard_detail` ADD CONSTRAINT `fk_ranking_detail_user` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE;

SELECT '排行榜表创建完成' as message;
