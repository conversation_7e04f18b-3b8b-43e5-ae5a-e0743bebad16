package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylpz.core.common.request.RankingRequest;
import com.ylpz.core.common.response.RankingDetailResponse;
import com.ylpz.core.common.response.RankingResponse;
import com.ylpz.core.common.response.RankingStatisticsResponse;
import com.ylpz.core.mapper.RankingLeaderboardDetailMapper;
import com.ylpz.core.mapper.RankingLeaderboardMapper;
import com.ylpz.core.service.RankingService;
import com.ylpz.core.service.UserBrokerageRecordService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.common.constants.BrokerageRecordConstants;
import com.ylpz.model.user.UserBrokerageRecord;
import com.ylpz.model.user.User;
import com.ylpz.model.ranking.RankingLeaderboard;
import com.ylpz.model.ranking.RankingLeaderboardDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 排行榜服务实现类
 */
@Slf4j
@Service
public class RankingServiceImpl implements RankingService {

    @Autowired
    private RankingLeaderboardMapper rankingLeaderboardMapper;

    @Autowired
    private RankingLeaderboardDetailMapper rankingLeaderboardDetailMapper;

    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;

    @Autowired
    private UserService userService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public IPage<RankingResponse> getRankingList(RankingRequest request, Integer page, Integer limit) {
        log.info("查询排行榜列表，参数：{}, 页码：{}, 每页数量：{}", request, page, limit);

        Page<RankingResponse> pageParam = new Page<>(page, limit);
        return rankingLeaderboardMapper.selectRankingList(pageParam, request);
    }

    @Override
    public RankingStatisticsResponse getRankingStatistics(RankingRequest request) {
        log.info("获取排行榜统计数据，参数：{}", request);

        return rankingLeaderboardMapper.selectRankingStatistics(request);
    }

    @Override
    public RankingDetailResponse getRankingDetail(Integer rankingId) {
        log.info("获取排行榜详细信息，排行榜ID：{}", rankingId);

        // 查询排行榜基本信息
        RankingLeaderboard ranking = rankingLeaderboardMapper.selectById(rankingId);
        if (ranking == null) {
            throw new RuntimeException("排行榜不存在");
        }

        // 构建响应对象
        RankingDetailResponse response = new RankingDetailResponse();

        // 设置排行榜信息
        RankingDetailResponse.RankingInfo rankingInfo = new RankingDetailResponse.RankingInfo();
        rankingInfo.setId(ranking.getId());
        rankingInfo.setRankType(ranking.getRankType());
        rankingInfo.setRankTypeName(getRankTypeName(ranking.getRankType()));
        rankingInfo.setRankPeriod(ranking.getRankPeriod());
        rankingInfo.setTotalSalesAmount(ranking.getTotalSalesAmount());
        rankingInfo.setParticipantCount(ranking.getParticipantCount());
        rankingInfo.setRewardStatus(ranking.getRewardStatus());
        rankingInfo.setRewardAmount(ranking.getRewardAmount());
        response.setRankingInfo(rankingInfo);

        // 查询排名详情
        List<RankingDetailResponse.RankItem> rankList = rankingLeaderboardDetailMapper
                .selectRankDetailByRankingId(rankingId);
        response.setRankList(rankList);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateRanking(String rankType, String startDate, String endDate) {
        log.info("生成排行榜，类型：{}，开始日期：{}，结束日期：{}", rankType, startDate, endDate);

        try {
            // 检查是否已存在该期排行榜
            RankingLeaderboard existRanking = rankingLeaderboardMapper.selectByRankTypeAndDate(rankType, startDate,
                    endDate);
            if (existRanking != null) {
                log.warn("该期排行榜已存在，排行榜ID：{}", existRanking.getId());
                return existRanking.getId();
            }

            // 查询销售排名数据
            List<RankingDetailResponse.RankItem> rankData = rankingLeaderboardDetailMapper
                    .selectSalesRankingByDateRange(
                            startDate + " 00:00:00", endDate + " 23:59:59", 0);

            if (rankData.isEmpty()) {
                log.warn("该时间段内无销售数据，无法生成排行榜");
                return null;
            }

            // 计算总销售金额和参与人数
            BigDecimal totalSalesAmount = rankData.stream()
                    .map(RankingDetailResponse.RankItem::getSalesAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            int participantCount = rankData.size();

            // 创建排行榜主记录
            RankingLeaderboard ranking = new RankingLeaderboard();
            ranking.setRankType(rankType);
            ranking.setRankPeriod(generateRankPeriod(rankType, startDate, endDate));
            ranking.setStartDate(DATE_FORMAT.parse(startDate));
            ranking.setEndDate(DATE_FORMAT.parse(endDate));
            ranking.setTotalSalesAmount(totalSalesAmount);
            ranking.setParticipantCount(participantCount);
            ranking.setRewardStatus("待发放");
            ranking.setRewardAmount(BigDecimal.ZERO);
            ranking.setIsAutoReward(true);
            ranking.setCreateTime(new Date());
            ranking.setUpdateTime(new Date());

            rankingLeaderboardMapper.insert(ranking);
            Integer rankingId = ranking.getId();

            // 创建排行榜明细记录
            List<RankingLeaderboardDetail> detailList = new ArrayList<>();
            for (RankingDetailResponse.RankItem item : rankData) {
                RankingLeaderboardDetail detail = new RankingLeaderboardDetail();
                detail.setRankingId(rankingId);
                detail.setUid(item.getUid());
                detail.setRank(item.getRank());
                detail.setSalesAmount(item.getSalesAmount());
                detail.setOrderCount(item.getOrderCount());
                detail.setRewardAmount(BigDecimal.ZERO);
                detail.setRewardStatus("待发放");
                detailList.add(detail);
            }

            if (!detailList.isEmpty()) {
                rankingLeaderboardDetailMapper.batchInsert(detailList);
            }

            log.info("排行榜生成成功，排行榜ID：{}，参与人数：{}", rankingId, participantCount);
            return rankingId;

        } catch (Exception e) {
            log.error("生成排行榜失败", e);
            throw new RuntimeException("生成排行榜失败：" + e.getMessage());
        }
    }

    /**
     * 获取排行类型名称
     */
    private String getRankTypeName(String rankType) {
        switch (rankType) {
            case "week":
                return "周榜";
            case "month":
                return "月榜";
            case "quarter":
                return "季度榜";
            case "year":
                return "年度榜";
            default:
                return rankType;
        }
    }

    /**
     * 生成排行周期描述
     */
    private String generateRankPeriod(String rankType, String startDate, String endDate) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            switch (rankType) {
                case "week":
                    return String.format("%d年%d月%d日-%d月%d日",
                            start.getYear(), start.getMonthValue(), start.getDayOfMonth(),
                            end.getMonthValue(), end.getDayOfMonth());
                case "month":
                    return String.format("%d年%d月", start.getYear(), start.getMonthValue());
                case "quarter":
                    int quarter = (start.getMonthValue() - 1) / 3 + 1;
                    return String.format("%d年第%d季度", start.getYear(), quarter);
                case "year":
                    return String.format("%d年度", start.getYear());
                default:
                    return String.format("%s-%s", startDate, endDate);
            }
        } catch (Exception e) {
            return String.format("%s-%s", startDate, endDate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean distributeReward(Integer rankingId) {
        log.info("手动发放排行榜奖励，排行榜ID：{}", rankingId);

        try {
            // 查询排行榜信息
            RankingLeaderboard ranking = rankingLeaderboardMapper.selectById(rankingId);
            if (ranking == null) {
                throw new RuntimeException("排行榜不存在");
            }

            if ("已发放".equals(ranking.getRewardStatus())) {
                log.warn("排行榜奖励已发放，无需重复发放");
                return true;
            }

            // 查询排行榜明细
            List<RankingDetailResponse.RankItem> rankList = rankingLeaderboardDetailMapper
                    .selectRankDetailByRankingId(rankingId);
            if (rankList.isEmpty()) {
                log.warn("排行榜明细为空，无法发放奖励");
                return false;
            }

            // 计算奖励金额（这里可以根据实际业务规则调整）
            BigDecimal totalRewardAmount = BigDecimal.ZERO;
            for (RankingDetailResponse.RankItem item : rankList) {
                BigDecimal rewardAmount = calculateRewardAmount(item.getRank(), item.getSalesAmount());
                if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 获取用户当前佣金余额
                    BigDecimal currentBalance = getUserBrokeragePrice(item.getUid());

                    // 创建奖励记录
                    UserBrokerageRecord record = new UserBrokerageRecord();
                    record.setUid(item.getUid());
                    record.setLinkId(rankingId.toString());
                    record.setLinkType("ranking");
                    record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
                    record.setTitle("排行榜奖励");
                    record.setPrice(rewardAmount);
                    record.setBalance(currentBalance.add(rewardAmount));
                    record.setMark("排行榜第" + item.getRank() + "名奖励");
                    record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
                    record.setCreateTime(new Date());
                    record.setUpdateTime(new Date());

                    // 保存奖励记录
                    userBrokerageRecordService.save(record);

                    // 更新用户佣金余额
                    userService.operationBrokerage(item.getUid(), rewardAmount, currentBalance, "add");

                    // 更新明细表的奖励信息
                    RankingLeaderboardDetail detail = new RankingLeaderboardDetail();
                    detail.setRankingId(rankingId);
                    detail.setUid(item.getUid());
                    detail.setRewardAmount(rewardAmount);
                    detail.setRewardStatus("已发放");
                    detail.setRewardTime(new Date());
                    rankingLeaderboardDetailMapper.updateById(detail);

                    totalRewardAmount = totalRewardAmount.add(rewardAmount);
                }
            }

            // 更新排行榜主表的奖励状态
            ranking.setRewardStatus("已发放");
            ranking.setRewardAmount(totalRewardAmount);
            ranking.setRewardTime(new Date());
            ranking.setUpdateTime(new Date());
            rankingLeaderboardMapper.updateById(ranking);

            log.info("排行榜奖励发放成功，总奖励金额：{}", totalRewardAmount);
            return true;

        } catch (Exception e) {
            log.error("发放排行榜奖励失败", e);
            throw new RuntimeException("发放排行榜奖励失败：" + e.getMessage());
        }
    }

    @Override
    public Integer batchDistributeReward(List<Integer> rankingIds) {
        log.info("批量发放排行榜奖励，排行榜ID列表：{}", rankingIds);

        int successCount = 0;
        for (Integer rankingId : rankingIds) {
            try {
                if (distributeReward(rankingId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("发放排行榜奖励失败，排行榜ID：{}", rankingId, e);
            }
        }

        log.info("批量发放排行榜奖励完成，成功数量：{}", successCount);
        return successCount;
    }

    @Override
    public void autoGenerateRanking() {
        log.info("开始自动生成排行榜");

        try {
            LocalDate now = LocalDate.now();

            // 生成上周周榜
            LocalDate lastWeekStart = now.minusWeeks(1)
                    .with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
            generateRanking("week", lastWeekStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                    lastWeekEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

            // 如果是月初，生成上月月榜
            if (now.getDayOfMonth() == 1) {
                LocalDate lastMonthStart = now.minusMonths(1).withDayOfMonth(1);
                LocalDate lastMonthEnd = now.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
                generateRanking("month", lastMonthStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastMonthEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            // 如果是季度初，生成上季度榜
            if (now.getDayOfMonth() == 1 && (now.getMonthValue() == 1 || now.getMonthValue() == 4 ||
                    now.getMonthValue() == 7 || now.getMonthValue() == 10)) {
                LocalDate lastQuarterStart = now.minusMonths(3).withDayOfMonth(1);
                LocalDate lastQuarterEnd = now.minusDays(1);
                generateRanking("quarter", lastQuarterStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastQuarterEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            // 如果是年初，生成上年年榜
            if (now.getDayOfMonth() == 1 && now.getMonthValue() == 1) {
                LocalDate lastYearStart = now.minusYears(1).withDayOfYear(1);
                LocalDate lastYearEnd = now.minusYears(1).with(TemporalAdjusters.lastDayOfYear());
                generateRanking("year", lastYearStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastYearEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            log.info("自动生成排行榜完成");

        } catch (Exception e) {
            log.error("自动生成排行榜失败", e);
        }
    }

    /**
     * 计算奖励金额
     * 根据排名和销售金额计算奖励，可以根据实际业务规则调整
     */
    private BigDecimal calculateRewardAmount(Integer rank, BigDecimal salesAmount) {
        if (rank == null || salesAmount == null || salesAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 简单的奖励规则：前10名有奖励
        if (rank <= 10) {
            // 第1名：销售额的5%
            if (rank == 1) {
                return salesAmount.multiply(new BigDecimal("0.05"));
            }
            // 第2-3名：销售额的3%
            else if (rank <= 3) {
                return salesAmount.multiply(new BigDecimal("0.03"));
            }
            // 第4-10名：销售额的1%
            else {
                return salesAmount.multiply(new BigDecimal("0.01"));
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取用户佣金余额
     */
    private BigDecimal getUserBrokeragePrice(Integer uid) {
        User user = userService.getById(uid);
        if (user != null && user.getBrokeragePrice() != null) {
            return user.getBrokeragePrice();
        }
        return BigDecimal.ZERO;
    }
}
