-- 销售数据功能相关SQL脚本

-- 为销售数据查询创建索引
CREATE INDEX IF NOT EXISTS idx_user_phone ON user(phone);
CREATE INDEX IF NOT EXISTS idx_user_level ON user(level);
CREATE INDEX IF NOT EXISTS idx_user_status ON user(status);
CREATE INDEX IF NOT EXISTS idx_store_order_uid_time ON store_order(uid, create_time);
CREATE INDEX IF NOT EXISTS idx_store_order_status ON store_order(status);
CREATE INDEX IF NOT EXISTS idx_store_order_paid ON store_order(paid);
CREATE INDEX IF NOT EXISTS idx_user_brokerage_record_uid_status ON user_brokerage_record(uid, status);
CREATE INDEX IF NOT EXISTS idx_user_brokerage_record_uid_type ON user_brokerage_record(uid, type);
