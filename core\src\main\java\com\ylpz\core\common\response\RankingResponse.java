package com.ylpz.core.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 排行榜响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RankingResponse对象", description = "排行榜响应对象")
public class RankingResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排行榜ID")
    private Integer id;

    @ApiModelProperty(value = "排行类型：week-周榜，month-月榜，quarter-季度榜，year-年度榜")
    private String rankType;

    @ApiModelProperty(value = "排行类型名称")
    private String rankTypeName;

    @ApiModelProperty(value = "排行周期描述，如'2025年1月27日-2月5日'")
    private String rankPeriod;

    @ApiModelProperty(value = "统计开始日期")
    private String startDate;

    @ApiModelProperty(value = "统计结束日期")
    private String endDate;

    @ApiModelProperty(value = "总销售金额")
    private BigDecimal totalSalesAmount;

    @ApiModelProperty(value = "参与人数")
    private Integer participantCount;

    @ApiModelProperty(value = "奖励状态：待发放、已发放、已取消")
    private String rewardStatus;

    @ApiModelProperty(value = "奖励总金额")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "是否自动发放奖励")
    private Boolean isAutoReward;

    @ApiModelProperty(value = "奖励发放时间")
    private String rewardTime;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
