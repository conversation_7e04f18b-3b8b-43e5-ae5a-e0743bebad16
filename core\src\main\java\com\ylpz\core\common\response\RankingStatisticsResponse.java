package com.ylpz.core.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 排行榜统计响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RankingStatisticsResponse对象", description = "排行榜统计响应对象")
public class RankingStatisticsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "累计销售额")
    private BigDecimal totalSalesAmount;

    @ApiModelProperty(value = "已计发奖励金额")
    private BigDecimal totalRewardAmount;

    @ApiModelProperty(value = "当期销售额")
    private BigDecimal currentPeriodSales;

    @ApiModelProperty(value = "待发放奖励数量")
    private Integer pendingRewardCount;

    @ApiModelProperty(value = "已完成奖励数量")
    private Integer completedRewardCount;

    @ApiModelProperty(value = "总排行榜期数")
    private Integer totalPeriodCount;
}
