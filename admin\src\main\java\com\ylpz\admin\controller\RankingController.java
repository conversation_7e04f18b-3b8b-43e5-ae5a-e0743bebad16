package com.ylpz.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.RankingRequest;
import com.ylpz.core.common.response.CommonResult;
import com.ylpz.core.common.response.RankingDetailResponse;
import com.ylpz.core.common.response.RankingResponse;
import com.ylpz.core.common.response.RankingStatisticsResponse;
import com.ylpz.core.service.RankingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 排行榜管理控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/ranking")
@Api(tags = "排行榜管理")
@Validated
public class RankingController {

    @Autowired
    private RankingService rankingService;

    /**
     * 获取排行榜列表
     */
    //@PreAuthorize("hasAuthority('admin:ranking:list')")
    @ApiOperation(value = "获取排行榜列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<IPage<RankingResponse>> getList(@Valid RankingRequest request, @Valid PageParamRequest pageParamRequest) {
        log.info("获取排行榜列表，参数：{}", request);
        IPage<RankingResponse> list = rankingService.getRankingList(request, pageParamRequest.getPage(), pageParamRequest.getLimit());
        return CommonResult.success(list);
    }

    /**
     * 获取排行榜统计数据
     */
    //@PreAuthorize("hasAuthority('admin:ranking:statistics')")
    @ApiOperation(value = "获取排行榜统计数据")
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    public CommonResult<RankingStatisticsResponse> getStatistics(@Valid RankingRequest request) {
        log.info("获取排行榜统计数据，参数：{}", request);
        RankingStatisticsResponse statistics = rankingService.getRankingStatistics(request);
        return CommonResult.success(statistics);
    }

    /**
     * 获取排行榜详细信息
     */
    //@PreAuthorize("hasAuthority('admin:ranking:detail')")
    @ApiOperation(value = "获取排行榜详细信息")
    @RequestMapping(value = "/detail/{rankingId}", method = RequestMethod.GET)
    public CommonResult<RankingDetailResponse> getDetail(@PathVariable @NotNull Integer rankingId) {
        log.info("获取排行榜详细信息，排行榜ID：{}", rankingId);
        RankingDetailResponse detail = rankingService.getRankingDetail(rankingId);
        return CommonResult.success(detail);
    }

    /**
     * 生成排行榜
     */
    //@PreAuthorize("hasAuthority('admin:ranking:generate')")
    @ApiOperation(value = "生成排行榜")
    @RequestMapping(value = "/generate", method = RequestMethod.POST)
    public CommonResult<Integer> generateRanking(@RequestParam String rankType,
                                                 @RequestParam String startDate,
                                                 @RequestParam String endDate) {
        log.info("生成排行榜，类型：{}，开始日期：{}，结束日期：{}", rankType, startDate, endDate);
        Integer rankingId = rankingService.generateRanking(rankType, startDate, endDate);
        return CommonResult.success(rankingId, "排行榜生成成功");
    }

    /**
     * 手动发放排行榜奖励
     */
    //@PreAuthorize("hasAuthority('admin:ranking:reward')")
    @ApiOperation(value = "手动发放排行榜奖励")
    @RequestMapping(value = "/reward/distribute", method = RequestMethod.POST)
    public CommonResult<Boolean> distributeReward(@RequestParam Integer rankingId) {
        log.info("手动发放排行榜奖励，排行榜ID：{}", rankingId);
        Boolean result = rankingService.distributeReward(rankingId);
        return CommonResult.success(result, "奖励发放成功");
    }

    /**
     * 批量发放排行榜奖励
     */
    //@PreAuthorize("hasAuthority('admin:ranking:reward')")
    @ApiOperation(value = "批量发放排行榜奖励")
    @RequestMapping(value = "/reward/batch", method = RequestMethod.POST)
    public CommonResult<Integer> batchDistributeReward(@RequestBody List<Integer> rankingIds) {
        log.info("批量发放排行榜奖励，排行榜ID列表：{}", rankingIds);
        Integer successCount = rankingService.batchDistributeReward(rankingIds);
        return CommonResult.success(successCount, "批量发放完成，成功数量：" + successCount);
    }
}
