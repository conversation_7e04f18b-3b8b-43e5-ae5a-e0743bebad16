package com.ylpz.entity;

public interface StoreProductAttrValueDefine {
    String image = "image";
    String brokerage = "brokerage";
    String cost = "cost";
    String quantity = "quantity";
    String productId = "productId";
    String otPrice = "otPrice";
    String suk = "suk";
    String weight = "weight";
    String type = "type";
    String sales = "sales";
    String barCode = "barCode";
    String brokerageTwo = "brokerageTwo";
    String volume = "volume";
    String price = "price";
    String unique = "unique";
    String quota = "quota";
    String quotaShow = "quotaShow";
    String id = "id";
    String attrValue = "attrValue";
    String stock = "stock";
    String isDel = "isDel";
}
