package com.ylpz.model.ranking;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 排行榜明细表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ranking_leaderboard_detail")
@ApiModel(value="RankingLeaderboardDetail对象", description="排行榜明细表")
public class RankingLeaderboardDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "明细ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "排行榜ID")
    private Integer rankingId;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "排名")
    private Integer rank;

    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmount;

    @ApiModelProperty(value = "订单数量")
    private Integer orderCount;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励状态：待发放、已发放、无奖励")
    private String rewardStatus;

    @ApiModelProperty(value = "奖励发放时间")
    private Date rewardTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
