package com.ylpz.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylpz.core.common.response.RankingDetailResponse;
import com.ylpz.model.ranking.RankingLeaderboardDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 排行榜明细表 Mapper 接口
 */
@Mapper
public interface RankingLeaderboardDetailMapper extends BaseMapper<RankingLeaderboardDetail> {

    /**
     * 根据排行榜ID查询排名详情
     *
     * @param rankingId 排行榜ID
     * @return 排名详情列表
     */
    List<RankingDetailResponse.RankItem> selectRankDetailByRankingId(@Param("rankingId") Integer rankingId);

    /**
     * 根据时间范围统计销售数据并生成排名
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param limit     限制条数，0表示不限制
     * @return 排名数据
     */
    List<RankingDetailResponse.RankItem> selectSalesRankingByDateRange(@Param("startDate") String startDate,
                                                                       @Param("endDate") String endDate,
                                                                       @Param("limit") Integer limit);

    /**
     * 批量插入排行榜明细
     *
     * @param detailList 明细列表
     * @return 插入数量
     */
    int batchInsert(@Param("detailList") List<RankingLeaderboardDetail> detailList);
}
