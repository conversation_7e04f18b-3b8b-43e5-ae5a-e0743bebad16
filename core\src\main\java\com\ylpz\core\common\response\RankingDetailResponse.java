package com.ylpz.core.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 排行榜详细响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RankingDetailResponse对象", description = "排行榜详细响应对象")
public class RankingDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排行榜信息")
    private RankingInfo rankingInfo;

    @ApiModelProperty(value = "排名列表")
    private List<RankItem> rankList;

    @Data
    @ApiModel(value = "RankingInfo", description = "排行榜信息")
    public static class RankingInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "排行榜ID")
        private Integer id;

        @ApiModelProperty(value = "排行类型")
        private String rankType;

        @ApiModelProperty(value = "排行类型名称")
        private String rankTypeName;

        @ApiModelProperty(value = "排行周期")
        private String rankPeriod;

        @ApiModelProperty(value = "总销售金额")
        private BigDecimal totalSalesAmount;

        @ApiModelProperty(value = "参与人数")
        private Integer participantCount;

        @ApiModelProperty(value = "奖励状态")
        private String rewardStatus;

        @ApiModelProperty(value = "奖励总金额")
        private BigDecimal rewardAmount;
    }

    @Data
    @ApiModel(value = "RankItem", description = "排名项")
    public static class RankItem implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "排名")
        private Integer rank;

        @ApiModelProperty(value = "用户ID")
        private Integer uid;

        @ApiModelProperty(value = "用户昵称")
        private String nickname;

        @ApiModelProperty(value = "手机号")
        private String phone;

        @ApiModelProperty(value = "头像")
        private String avatar;

        @ApiModelProperty(value = "会员等级")
        private String memberLevel;

        @ApiModelProperty(value = "销售金额")
        private BigDecimal salesAmount;

        @ApiModelProperty(value = "订单数量")
        private Integer orderCount;

        @ApiModelProperty(value = "奖励金额")
        private BigDecimal rewardAmount;

        @ApiModelProperty(value = "奖励状态")
        private String rewardStatus;

        @ApiModelProperty(value = "奖励发放时间")
        private String rewardTime;
    }
}
