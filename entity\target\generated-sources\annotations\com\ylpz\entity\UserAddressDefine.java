package com.ylpz.entity;

public interface UserAddressDefine {
    String city = "city";
    String latitude = "latitude";
    String updateTime = "updateTime";
    String cityId = "cityId";
    String uid = "uid";
    String realName = "realName";
    String isDefault = "isDefault";
    String province = "province";
    String phone = "phone";
    String createTime = "createTime";
    String district = "district";
    String postCode = "postCode";
    String id = "id";
    String detail = "detail";
    String isDel = "isDel";
    String longitude = "longitude";
}
