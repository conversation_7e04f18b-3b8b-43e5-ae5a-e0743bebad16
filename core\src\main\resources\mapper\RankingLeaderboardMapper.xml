<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.mapper.RankingLeaderboardMapper">

    <!-- 分页查询排行榜列表 -->
    <select id="selectRankingList" resultType="com.ylpz.core.common.response.RankingResponse">
        SELECT
            rl.id,
            rl.rank_type AS rankType,
            CASE rl.rank_type
                WHEN 'week' THEN '周榜'
                WHEN 'month' THEN '月榜'
                WHEN 'quarter' THEN '季度榜'
                WHEN 'year' THEN '年度榜'
                ELSE rl.rank_type
            END AS rankTypeName,
            rl.rank_period AS rankPeriod,
            DATE_FORMAT(rl.start_date, '%Y-%m-%d') AS startDate,
            DATE_FORMAT(rl.end_date, '%Y-%m-%d') AS endDate,
            rl.total_sales_amount AS totalSalesAmount,
            rl.participant_count AS participantCount,
            rl.reward_status AS rewardStatus,
            rl.reward_amount AS rewardAmount,
            rl.is_auto_reward AS isAutoReward,
            DATE_FORMAT(rl.reward_time, '%Y-%m-%d %H:%i:%s') AS rewardTime,
            DATE_FORMAT(rl.create_time, '%Y-%m-%d %H:%i:%s') AS createTime
        FROM ranking_leaderboard rl
        <where>
            <if test="request.rankType != null and request.rankType != ''">
                AND rl.rank_type = #{request.rankType}
            </if>
            <if test="request.year != null">
                AND YEAR(rl.start_date) = #{request.year}
            </if>
            <if test="request.rewardStatus != null and request.rewardStatus != ''">
                AND rl.reward_status = #{request.rewardStatus}
            </if>
            <if test="request.startTime != null and request.startTime != ''">
                AND rl.start_date >= #{request.startTime}
            </if>
            <if test="request.endTime != null and request.endTime != ''">
                AND rl.end_date <= #{request.endTime}
            </if>
        </where>
        ORDER BY rl.start_date DESC, rl.id DESC
    </select>

    <!-- 获取排行榜统计数据 -->
    <select id="selectRankingStatistics" resultType="com.ylpz.core.common.response.RankingStatisticsResponse">
        SELECT
            COALESCE(SUM(rl.total_sales_amount), 0) AS totalSalesAmount,
            COALESCE(SUM(CASE WHEN rl.reward_status = '已发放' THEN rl.reward_amount ELSE 0 END), 0) AS totalRewardAmount,
            COALESCE((
                SELECT rl2.total_sales_amount
                FROM ranking_leaderboard rl2
                WHERE 1=1
                <if test="request.rankType != null and request.rankType != ''">
                    AND rl2.rank_type = #{request.rankType}
                </if>
                ORDER BY rl2.start_date DESC
                LIMIT 1
            ), 0) AS currentPeriodSales,
            COALESCE(SUM(CASE WHEN rl.reward_status = '待发放' THEN 1 ELSE 0 END), 0) AS pendingRewardCount,
            COALESCE(SUM(CASE WHEN rl.reward_status = '已发放' THEN 1 ELSE 0 END), 0) AS completedRewardCount,
            COUNT(*) AS totalPeriodCount
        FROM ranking_leaderboard rl
        <where>
            <if test="request.rankType != null and request.rankType != ''">
                AND rl.rank_type = #{request.rankType}
            </if>
            <if test="request.year != null">
                AND YEAR(rl.start_date) = #{request.year}
            </if>
        </where>
    </select>

    <!-- 根据排行类型和时间范围查询排行榜 -->
    <select id="selectByRankTypeAndDate" resultType="com.ylpz.model.ranking.RankingLeaderboard">
        SELECT * FROM ranking_leaderboard
        WHERE rank_type = #{rankType}
          AND start_date = #{startDate}
          AND end_date = #{endDate}
        LIMIT 1
    </select>

</mapper>
