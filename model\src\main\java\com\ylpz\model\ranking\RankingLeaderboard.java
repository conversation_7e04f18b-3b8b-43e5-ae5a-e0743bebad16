package com.ylpz.model.ranking;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 排行榜主表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ranking_leaderboard")
@ApiModel(value="RankingLeaderboard对象", description="排行榜主表")
public class RankingLeaderboard implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排行榜ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "排行类型：week-周榜，month-月榜，quarter-季度榜，year-年度榜")
    private String rankType;

    @ApiModelProperty(value = "排行周期描述，如'2025年1月27日-2月5日'")
    private String rankPeriod;

    @ApiModelProperty(value = "统计开始日期")
    private Date startDate;

    @ApiModelProperty(value = "统计结束日期")
    private Date endDate;

    @ApiModelProperty(value = "总销售金额")
    private BigDecimal totalSalesAmount;

    @ApiModelProperty(value = "参与人数")
    private Integer participantCount;

    @ApiModelProperty(value = "奖励状态：待发放、已发放、已取消")
    private String rewardStatus;

    @ApiModelProperty(value = "奖励总金额")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "是否自动发放奖励：1-是，0-否")
    private Boolean isAutoReward;

    @ApiModelProperty(value = "奖励发放时间")
    private Date rewardTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
