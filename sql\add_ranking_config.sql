-- 添加排行榜相关配置到会员参数配置表

-- 检查member_param_config表是否存在，如果不存在则使用system_member_param_config表
SET @table_name = (
    SELECT CASE 
        WHEN COUNT(*) > 0 THEN 'member_param_config'
        ELSE 'system_member_param_config'
    END
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'member_param_config'
);

-- 删除旧的排行榜配置（如果存在）
DELETE FROM member_param_config WHERE config_type = '排行榜设置';
DELETE FROM member_param_config WHERE config_key LIKE 'ranking_%';

-- 插入排行榜显示设置配置
INSERT IGNORE INTO `member_param_config` (`config_type`, `config_key`, `config_name`, `config_value`, `unit`, `ratio`, `number`, `status`, `sort`, `remark`) VALUES
('排行榜设置', 'ranking_display_count', '榜单排名展示数量', 'TOP20', '', NULL, '20', 1, 50, '排行榜显示的排名数量，如TOP20表示显示前20名'),
('排行榜设置', 'ranking_week_threshold', '周榜入榜门槛值', '10000', '元', NULL, '10000', 1, 51, '周榜入榜最低销售金额，低于此金额不显示在榜单中'),
('排行榜设置', 'ranking_month_threshold', '月榜入榜门槛值', '30000', '元', NULL, '30000', 1, 52, '月榜入榜最低销售金额，低于此金额不显示在榜单中'),
('排行榜设置', 'ranking_quarter_threshold', '季度榜入榜门槛值', '100000', '元', NULL, '100000', 1, 53, '季度榜入榜最低销售金额，低于此金额不显示在榜单中'),
('排行榜设置', 'ranking_year_threshold', '年榜入榜门槛值', '500000', '元', NULL, '500000', 1, 54, '年榜入榜最低销售金额，低于此金额不显示在榜单中'),
('排行榜设置', 'ranking_reward_count', '奖励人数', '3', '名', NULL, '3', 1, 55, '排行榜奖励前几名，如3表示奖励前3名');

-- 插入周榜奖励配置
INSERT IGNORE INTO `member_param_config` (`config_type`, `config_key`, `config_name`, `config_value`, `unit`, `ratio`, `number`, `status`, `sort`, `remark`) VALUES
('奖励金设置', 'ranking_week_first', '周榜第一名奖励', '500', '元', NULL, '500', 1, 61, '周榜第一名奖励金额'),
('奖励金设置', 'ranking_week_second', '周榜第二名奖励', '300', '元', NULL, '300', 1, 62, '周榜第二名奖励金额'),
('奖励金设置', 'ranking_week_third', '周榜第三名奖励', '200', '元', NULL, '200', 1, 63, '周榜第三名奖励金额');

-- 插入月榜奖励配置
INSERT IGNORE INTO `member_param_config` (`config_type`, `config_key`, `config_name`, `config_value`, `unit`, `ratio`, `number`, `status`, `sort`, `remark`) VALUES
('奖励金设置', 'ranking_month_first', '月榜第一名奖励', '1000', '元', NULL, '1000', 1, 64, '月榜第一名奖励金额'),
('奖励金设置', 'ranking_month_second', '月榜第二名奖励', '600', '元', NULL, '600', 1, 65, '月榜第二名奖励金额'),
('奖励金设置', 'ranking_month_third', '月榜第三名奖励', '400', '元', NULL, '400', 1, 66, '月榜第三名奖励金额');

-- 插入季度榜奖励配置
INSERT IGNORE INTO `member_param_config` (`config_type`, `config_key`, `config_name`, `config_value`, `unit`, `ratio`, `number`, `status`, `sort`, `remark`) VALUES
('奖励金设置', 'ranking_quarter_first', '季度榜第一名奖励', '2000', '元', NULL, '2000', 1, 67, '季度榜第一名奖励金额'),
('奖励金设置', 'ranking_quarter_second', '季度榜第二名奖励', '1200', '元', NULL, '1200', 1, 68, '季度榜第二名奖励金额'),
('奖励金设置', 'ranking_quarter_third', '季度榜第三名奖励', '800', '元', NULL, '800', 1, 69, '季度榜第三名奖励金额');

-- 插入年榜奖励配置
INSERT IGNORE INTO `member_param_config` (`config_type`, `config_key`, `config_name`, `config_value`, `unit`, `ratio`, `number`, `status`, `sort`, `remark`) VALUES
('奖励金设置', 'ranking_year_first', '年榜第一名奖励', '5000', '元', NULL, '5000', 1, 70, '年榜第一名奖励金额'),
('奖励金设置', 'ranking_year_second', '年榜第二名奖励', '3000', '元', NULL, '3000', 1, 71, '年榜第二名奖励金额'),
('奖励金设置', 'ranking_year_third', '年榜第三名奖励', '2000', '元', NULL, '2000', 1, 72, '年榜第三名奖励金额');

-- 显示插入结果
SELECT '排行榜配置添加完成' as message;
SELECT COUNT(*) as ranking_configs FROM member_param_config WHERE config_type IN ('排行榜设置', '奖励金设置') AND config_key LIKE 'ranking_%';
